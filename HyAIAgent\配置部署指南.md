# ⚙️ HyAIAgent 配置部署指南

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.9+
- **内存**: 最低 2GB RAM，推荐 4GB+
- **存储**: 最低 1GB 可用空间
- **网络**: 稳定的互联网连接（用于AI API调用）

### 依赖包清单
```txt
# 核心框架
asyncio>=3.4.3
aiohttp>=3.8.0
aiofiles>=0.8.0

# AI客户端
openai>=1.0.0
anthropic>=0.7.0

# 数据库
aiosqlite>=0.18.0

# 网络搜索
tavily-python>=0.3.0

# GUI界面
PyQt6>=6.4.0

# 工具库
python-dotenv>=1.0.0
loguru>=0.6.0
pydantic>=1.10.0
```

## 🔧 配置文件结构

### 主配置文件 (config.json)
```json
{
  "application": {
    "name": "HyAIAgent",
    "version": "1.0.0",
    "debug": false,
    "log_level": "INFO",
    "data_directory": "./data",
    "workspace_directory": "./workspace"
  },
  "ai_providers": {
    "default": "openai",
    "openai": {
      "api_key": "${OPENAI_API_KEY}",
      "base_url": "https://api.openai.com/v1",
      "model": "gpt-4",
      "max_tokens": 4000,
      "temperature": 0.7,
      "timeout": 30
    },
    "anthropic": {
      "api_key": "${ANTHROPIC_API_KEY}",
      "base_url": "https://api.anthropic.com",
      "model": "claude-3-sonnet-20240229",
      "max_tokens": 4000,
      "temperature": 0.7,
      "timeout": 30
    }
  },
  "database": {
    "type": "sqlite",
    "path": "data/hyaiagent.db",
    "backup_enabled": true,
    "backup_interval": 3600
  },
  "search": {
    "provider": "tavily",
    "tavily": {
      "api_key": "${TAVILY_API_KEY}",
      "max_results": 5,
      "search_depth": "basic",
      "timeout": 10
    }
  },
  "file_operations": {
    "base_path": "./workspace",
    "allowed_extensions": [
      ".txt", ".md", ".json", ".py", ".js", 
      ".html", ".css", ".xml", ".yaml", ".yml"
    ],
    "max_file_size": 10485760,
    "backup_enabled": true,
    "encoding": "utf-8"
  },
  "task_management": {
    "max_concurrent_tasks": 3,
    "task_timeout": 300,
    "cleanup_interval": 1800,
    "max_history": 500
  },
  "cache": {
    "max_entries": 1000,
    "cleanup_threshold": 0.8,
    "default_ttl": 3600
  },
  "ui": {
    "window_title": "HyAIAgent - 智能助手",
    "window_size": [1000, 700],
    "font_family": "Microsoft YaHei",
    "font_size": 10,
    "theme": "light"
  },
  "logging": {
    "level": "INFO",
    "file_path": "logs/hyaiagent.log",
    "max_file_size": "10MB",
    "backup_count": 5,
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  },
  "security": {
    "max_request_size": 1048576,
    "rate_limit": {
      "requests_per_minute": 30,
      "requests_per_hour": 500
    },
    "allowed_file_operations": [
      "read", "write", "delete", "list", "exists"
    ]
  }
}
```

### 环境变量配置 (.env)
```env
# AI API配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# 应用配置
DEBUG=false
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your_secret_key_here
```

## 🚀 部署步骤

### 1. 环境准备
```bash
# 创建项目目录
mkdir HyAIAgent
cd HyAIAgent

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2. 安装依赖
```bash
# 安装核心依赖
pip install -r requirements.txt

# 验证安装
python -c "import PyQt6; print('PyQt6 安装成功')"
python -c "import openai; print('OpenAI 安装成功')"
```

### 3. 项目初始化
```bash
# 创建必要目录
mkdir -p data logs workspace

# 复制配置文件
cp config_example.json config.json

# 创建环境变量文件
touch .env
```

### 4. 配置设置
```bash
# 编辑环境变量文件
# 添加您的API密钥
echo "OPENAI_API_KEY=your_key_here" >> .env
echo "ANTHROPIC_API_KEY=your_key_here" >> .env
echo "TAVILY_API_KEY=your_key_here" >> .env
```

### 5. 数据库初始化
```bash
# 运行数据库初始化脚本
python -c "
import asyncio
from core.database import HyAIDatabase

async def init_db():
    db = HyAIDatabase()
    await db.init_database()
    await db.close()
    print('数据库初始化完成')

asyncio.run(init_db())
"
```

### 6. 配置验证
```bash
# 验证配置文件
python -c "
from core.config_manager import ConfigManager
config = ConfigManager()
print('配置文件加载成功')
print(f'应用名称: {config.get(\"application.name\")}')
"

# 测试AI连接
python -c "
import asyncio
from core.ai_client import AIClientManager
from core.config_manager import ConfigManager

async def test_ai():
    config = ConfigManager()
    ai_client = AIClientManager(config.config)
    try:
        response = await ai_client.chat_completion([
            {'role': 'user', 'content': '你好'}
        ])
        print('AI连接测试成功')
        print(f'回复: {response[:50]}...')
    except Exception as e:
        print(f'AI连接测试失败: {e}')

asyncio.run(test_ai())
"
```

## 🐳 Docker部署（可选）

### Dockerfile
```dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    qt6-base-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs workspace

# 设置权限
RUN chmod +x main.py

# 暴露端口（如果有Web界面）
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  hyaiagent:
    build: .
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
    volumes:
      - ./workspace:/app/workspace
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
```

## 🔧 运维管理

### 日志管理
```python
# 日志配置示例
from loguru import logger

# 配置日志格式
logger.add(
    "logs/hyaiagent_{time:YYYY-MM-DD}.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)

# 日志使用示例
logger.info("应用启动")
logger.error("发生错误: {error}", error="示例错误")
```

### 备份策略
```bash
#!/bin/bash
# 数据备份脚本 (backup.sh)

# 备份数据库
cp data/hyaiagent.db backups/db_backup_$(date +%Y%m%d_%H%M%S).db

# 备份配置文件
tar -czf backups/config_backup_$(date +%Y%m%d_%H%M%S).tar.gz config.json .env

# 备份工作空间
tar -czf backups/workspace_backup_$(date +%Y%m%d_%H%M%S).tar.gz workspace/

# 清理旧备份（保留7天）
find backups/ -name "*.db" -mtime +7 -delete
find backups/ -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $(date)"
```

### 监控脚本
```python
# monitor.py - 系统监控脚本
import psutil
import time
from pathlib import Path

def check_system_health():
    """检查系统健康状态"""
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
    
    # 磁盘使用率
    disk = psutil.disk_usage('.')
    disk_percent = (disk.used / disk.total) * 100
    
    # 检查数据库文件
    db_path = Path('data/hyaiagent.db')
    db_exists = db_path.exists()
    db_size = db_path.stat().st_size if db_exists else 0
    
    print(f"系统状态检查 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CPU使用率: {cpu_percent:.1f}%")
    print(f"内存使用率: {memory_percent:.1f}%")
    print(f"磁盘使用率: {disk_percent:.1f}%")
    print(f"数据库状态: {'正常' if db_exists else '异常'}")
    print(f"数据库大小: {db_size / 1024 / 1024:.2f} MB")
    print("-" * 50)

if __name__ == "__main__":
    while True:
        check_system_health()
        time.sleep(300)  # 每5分钟检查一次
```

## 🔒 安全配置

### API密钥管理
- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 不要将密钥提交到版本控制

### 文件安全
- 限制文件操作范围在工作目录内
- 检查文件扩展名白名单
- 限制文件大小和操作频率

### 网络安全
- 实施API调用速率限制
- 验证外部API响应
- 使用HTTPS连接

## 🚀 启动应用

### 开发模式
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 设置调试模式
export DEBUG=true

# 启动应用
python main.py
```

### 生产模式
```bash
# 确保环境变量正确设置
export DEBUG=false
export LOG_LEVEL=INFO

# 启动应用
python main.py

# 或使用nohup在后台运行
nohup python main.py > logs/app.log 2>&1 &
```

## 📊 性能调优

### 数据库优化
```sql
-- 创建索引提高查询性能
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_conversations_session ON conversations(session_id);
CREATE INDEX idx_cache_key ON ai_cache(cache_key);
```

### 内存优化
```python
# 配置文件中的内存优化设置
{
  "cache": {
    "max_entries": 500,  # 减少缓存条目
    "cleanup_threshold": 0.7  # 更早清理
  },
  "task_management": {
    "max_concurrent_tasks": 2,  # 减少并发任务
    "max_history": 200  # 减少历史记录
  }
}
```

---

**🎯 部署目标**: 提供完整的配置和部署指导，确保HyAIAgent能够在各种环境中稳定运行。
