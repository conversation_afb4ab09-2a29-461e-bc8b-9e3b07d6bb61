{"application": {"name": "HyAIAgent", "version": "1.0.0", "debug": false, "log_level": "INFO", "data_directory": "./data", "workspace_directory": "./workspace"}, "ai_providers": {"default": "openai", "openai": {"api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1", "model": "gpt-4", "max_tokens": 4000, "temperature": 0.7, "timeout": 30}, "deepseek": {"api_key": "${DEEPSEEK_API_KEY}", "base_url": "https://api.deepseek.com/v1", "model": "deepseek-chat", "max_tokens": 4000, "temperature": 0.7, "timeout": 30}, "zhipu": {"api_key": "${ZHIPU_API_KEY}", "base_url": "https://open.bigmodel.cn/api/paas/v4", "model": "glm-4", "max_tokens": 4000, "temperature": 0.7, "timeout": 30}}, "database": {"sqlite": {"path": "data/hyaiagent.db", "backup_enabled": true, "backup_interval": 3600, "auto_vacuum": true, "journal_mode": "WAL"}, "kv_store": {"path": "data/kv_store.json", "auto_cleanup": true, "cleanup_interval": 3600, "default_ttl": null}}, "prompts": {"directory": "prompts", "global_prompts": {"execution_orchestrator": "global/execution_orchestrator.md", "task_analyzer": "global/task_analyzer.md", "response_formatter": "global/response_formatter.md"}, "task_prompts": {"file_operations": "tasks/file_operations.md", "data_analysis": "tasks/data_analysis.md", "web_search": "tasks/web_search.md", "code_generation": "tasks/code_generation.md", "content_creation": "tasks/content_creation.md"}, "cache_enabled": true, "template_engine": "jinja2"}, "search": {"provider": "tavily", "tavily": {"api_key": "${TAVILY_API_KEY}", "max_results": 5, "search_depth": "basic", "timeout": 10}}, "file_operations": {"base_path": "./workspace", "allowed_extensions": [".txt", ".md", ".json", ".py", ".js", ".html", ".css", ".xml", ".yaml", ".yml"], "max_file_size": 10485760, "backup_enabled": true, "encoding": "utf-8"}, "task_management": {"max_concurrent_tasks": 3, "task_timeout": 300, "cleanup_interval": 1800, "max_history": 500}, "cache": {"max_entries": 1000, "cleanup_threshold": 0.8, "default_ttl": 3600}, "ui": {"window_title": "HyAIAgent - 智能助手", "window_size": [1000, 700], "font_family": "Microsoft YaHei", "font_size": 10, "theme": "light"}, "logging": {"level": "INFO", "file_path": "logs/hyaiagent.log", "max_file_size": "10MB", "backup_count": 5, "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"}, "security": {"max_request_size": 1048576, "rate_limit": {"requests_per_minute": 30, "requests_per_hour": 500}, "allowed_file_operations": ["read", "write", "delete", "list", "exists"]}}