# 🥇 第一阶段：基础AI问答系统实现指南

## 🎯 阶段目标

构建一个简单但完整的AI聊天应用，用户可以通过图形界面与OpenAI GPT模型进行对话。这是整个HyAIAgent项目的基础版本。

## 📋 功能清单

### ✅ 核心功能
- [x] 基础配置管理（读取API密钥等配置）
- [x] OpenAI兼容API客户端集成（支持多种AI服务商）
- [x] 基础提示词系统（全局执行提示词）
- [x] 轻量级KV数据库（TinyDB）
- [x] PyQt6图形界面（聊天窗口）
- [x] 对话历史管理（KV数据库保存）
- [x] 基础错误处理和日志

### 🚫 不包含功能
- 复杂任务分解（第二阶段添加）
- 任务专用提示词（第二阶段添加）
- 文件操作（第三阶段添加）
- 网络搜索（第四阶段添加）

## 🏗️ 项目结构

```
HyAIAgent_Stage1/
├── main.py                    # 程序入口
├── config.json               # 配置文件
├── .env                       # 环境变量（API密钥）
├── requirements.txt           # 依赖包
├── README.md                  # 使用说明
├── core/                      # 核心模块
│   ├── __init__.py
│   ├── config_manager.py     # 配置管理
│   ├── ai_client.py          # AI客户端
│   ├── kv_store.py           # KV数据库
│   └── prompt_manager.py     # 提示词管理
├── prompts/                   # 提示词目录
│   ├── global/               # 全局提示词
│   │   └── basic_chat.md     # 基础聊天提示词
│   └── templates/            # 提示词模板
├── data/                      # 数据目录
│   └── kv_store.json         # KV数据库文件
└── ui/                        # 用户界面
    ├── __init__.py
    └── chat_window.py        # 聊天窗口
```

## 📦 依赖包配置

### requirements.txt
```txt
openai>=1.0.0
PyQt6>=6.4.0
python-dotenv>=1.0.0
loguru>=0.6.0
tinydb>=4.8.0
jinja2>=3.1.0
```

### 安装命令
```bash
pip install -r requirements.txt
```

## ⚙️ 配置文件

### config.json
```json
{
  "application": {
    "name": "HyAIAgent",
    "version": "1.0.0-stage1",
    "window_title": "HyAIAgent - AI助手"
  },
  "ai_providers": {
    "default": "openai",
    "openai": {
      "api_key": "${OPENAI_API_KEY}",
      "base_url": "https://api.openai.com/v1",
      "model": "gpt-3.5-turbo",
      "max_tokens": 1000,
      "temperature": 0.7,
      "timeout": 30
    },
    "deepseek": {
      "api_key": "${DEEPSEEK_API_KEY}",
      "base_url": "https://api.deepseek.com/v1",
      "model": "deepseek-chat",
      "max_tokens": 1000,
      "temperature": 0.7,
      "timeout": 30
    }
  },
  "kv_database": {
    "path": "data/kv_store.json",
    "auto_cleanup": true,
    "cleanup_interval": 3600
  },
  "ui": {
    "window_size": [800, 600],
    "font_family": "Microsoft YaHei",
    "font_size": 10
  },
  "logging": {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
  }
}
```

### .env
```env
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
ZHIPU_API_KEY=your_zhipu_api_key_here
```

## 💻 核心代码实现

### 1. 配置管理器 (core/config_manager.py)
```python
import json
import os
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = Path(config_path)
        self.config = {}
        self.load_config()
        self.load_env()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = self.get_default_config()
                self.save_config()
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            self.config = self.get_default_config()
    
    def load_env(self):
        """加载环境变量"""
        load_dotenv()
        
        # 从环境变量获取API密钥
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            if 'ai' not in self.config:
                self.config['ai'] = {}
            self.config['ai']['api_key'] = openai_key
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "application": {
                "name": "HyAIAgent",
                "version": "1.0.0-stage1",
                "window_title": "HyAIAgent - AI助手"
            },
            "ai": {
                "provider": "openai",
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7,
                "timeout": 30
            },
            "ui": {
                "window_size": [800, 600],
                "font_family": "Microsoft YaHei",
                "font_size": 10
            },
            "logging": {
                "level": "INFO",
                "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
            }
        }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值（支持点号分隔的路径）"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        self.save_config()
```

### 2. AI客户端 (core/ai_client.py)
```python
import openai
from typing import List, Dict, Optional
from loguru import logger

class SimpleAIClient:
    """简单的AI客户端"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", 
                 max_tokens: int = 1000, temperature: float = 0.7):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.conversation_history = []
        
        logger.info(f"AI客户端初始化完成，模型: {model}")
    
    def chat(self, message: str) -> str:
        """发送消息并获取AI回复"""
        try:
            # 添加用户消息到历史
            self.conversation_history.append({
                "role": "user", 
                "content": message
            })
            
            logger.info(f"发送消息: {message[:50]}...")
            
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=self.conversation_history,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            # 获取AI回复
            ai_response = response.choices[0].message.content
            
            # 添加AI回复到历史
            self.conversation_history.append({
                "role": "assistant",
                "content": ai_response
            })
            
            logger.info(f"收到回复: {ai_response[:50]}...")
            return ai_response
            
        except openai.APIError as e:
            error_msg = f"OpenAI API错误: {str(e)}"
            logger.error(error_msg)
            return f"抱歉，发生了API错误: {str(e)}"
            
        except openai.RateLimitError as e:
            error_msg = "API调用频率超限，请稍后再试"
            logger.error(error_msg)
            return error_msg
            
        except Exception as e:
            error_msg = f"发生未知错误: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
        logger.info("对话历史已清空")
    
    def get_history(self) -> List[Dict[str, str]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def get_history_summary(self) -> str:
        """获取对话历史摘要"""
        if not self.conversation_history:
            return "暂无对话历史"
        
        user_count = sum(1 for msg in self.conversation_history if msg['role'] == 'user')
        assistant_count = sum(1 for msg in self.conversation_history if msg['role'] == 'assistant')
        
        return f"对话轮次: {user_count}, AI回复: {assistant_count}"
```

### 3. 聊天窗口 (ui/chat_window.py)
```python
import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QHBoxLayout, QWidget, QTextEdit, QLineEdit, 
                            QPushButton, QLabel, QMessageBox, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont
from loguru import logger

from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient

class ChatWorker(QThread):
    """聊天工作线程"""
    response_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, ai_client, message):
        super().__init__()
        self.ai_client = ai_client
        self.message = message
    
    def run(self):
        try:
            response = self.ai_client.chat(self.message)
            self.response_received.emit(response)
        except Exception as e:
            self.error_occurred.emit(str(e))

class ChatWindow(QMainWindow):
    """聊天窗口"""
    
    def __init__(self):
        super().__init__()
        self.config = ConfigManager()
        self.ai_client = None
        self.chat_worker = None
        
        self.init_ai_client()
        self.init_ui()
        self.setup_logging()
    
    def init_ai_client(self):
        """初始化AI客户端"""
        try:
            api_key = self.config.get('ai.api_key')
            if not api_key:
                QMessageBox.critical(self, "错误", "未找到OpenAI API密钥，请检查.env文件")
                sys.exit(1)
            
            self.ai_client = SimpleAIClient(
                api_key=api_key,
                model=self.config.get('ai.model', 'gpt-3.5-turbo'),
                max_tokens=self.config.get('ai.max_tokens', 1000),
                temperature=self.config.get('ai.temperature', 0.7)
            )
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"AI客户端初始化失败: {str(e)}")
            sys.exit(1)
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.config.get('application.window_title', 'HyAIAgent'))
        
        # 设置窗口大小
        window_size = self.config.get('ui.window_size', [800, 600])
        self.resize(window_size[0], window_size[1])
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 创建聊天显示区域
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont(
            self.config.get('ui.font_family', 'Microsoft YaHei'),
            self.config.get('ui.font_size', 10)
        ))
        layout.addWidget(self.chat_display)
        
        # 创建输入区域
        input_layout = QHBoxLayout()
        
        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText("请输入您的消息...")
        self.message_input.returnPressed.connect(self.send_message)
        input_layout.addWidget(self.message_input)
        
        self.send_button = QPushButton("发送")
        self.send_button.clicked.connect(self.send_message)
        input_layout.addWidget(self.send_button)
        
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_chat)
        input_layout.addWidget(self.clear_button)
        
        layout.addLayout(input_layout)
        
        # 设置焦点
        self.message_input.setFocus()
        
        logger.info("聊天窗口初始化完成")
    
    def setup_logging(self):
        """设置日志"""
        from loguru import logger
        logger.add(
            "logs/hyaiagent_stage1.log",
            rotation="1 day",
            retention="7 days",
            level=self.config.get('logging.level', 'INFO'),
            format=self.config.get('logging.format', 
                                 "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
        )
    
    def send_message(self):
        """发送消息"""
        message = self.message_input.text().strip()
        if not message:
            return
        
        # 显示用户消息
        self.append_message("用户", message)
        self.message_input.clear()
        
        # 禁用输入
        self.set_input_enabled(False)
        self.status_label.setText("AI正在思考...")
        
        # 创建工作线程
        self.chat_worker = ChatWorker(self.ai_client, message)
        self.chat_worker.response_received.connect(self.on_response_received)
        self.chat_worker.error_occurred.connect(self.on_error_occurred)
        self.chat_worker.start()
    
    def on_response_received(self, response):
        """处理AI回复"""
        self.append_message("AI助手", response)
        self.set_input_enabled(True)
        self.status_label.setText("就绪")
        self.message_input.setFocus()
    
    def on_error_occurred(self, error):
        """处理错误"""
        self.append_message("系统", f"错误: {error}")
        self.set_input_enabled(True)
        self.status_label.setText("发生错误")
        self.message_input.setFocus()
    
    def append_message(self, sender, message):
        """添加消息到显示区域"""
        self.chat_display.append(f"<b>{sender}:</b> {message}")
        self.chat_display.append("")  # 空行
    
    def set_input_enabled(self, enabled):
        """设置输入控件状态"""
        self.message_input.setEnabled(enabled)
        self.send_button.setEnabled(enabled)
    
    def clear_chat(self):
        """清空聊天"""
        reply = QMessageBox.question(self, "确认", "确定要清空聊天记录吗？")
        if reply == QMessageBox.StandardButton.Yes:
            self.chat_display.clear()
            self.ai_client.clear_history()
            self.status_label.setText("聊天记录已清空")
            logger.info("聊天记录已清空")
```

### 4. 程序入口 (main.py)
```python
import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.chat_window import ChatWindow

def main():
    """主函数"""
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    
    # 配置日志
    logger.add(
        "logs/hyaiagent_stage1.log",
        rotation="1 day",
        retention="7 days",
        level="INFO"
    )
    
    logger.info("HyAIAgent Stage1 启动")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = ChatWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
```

## 🚀 运行步骤

### 1. 环境准备
```bash
# 创建项目目录
mkdir HyAIAgent_Stage1
cd HyAIAgent_Stage1

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置
```bash
# 创建.env文件并添加API密钥
echo OPENAI_API_KEY=your_api_key_here > .env
```

### 3. 启动应用
```bash
python main.py
```

## ✅ 验收标准

### 功能验收
- [ ] 应用能够正常启动并显示聊天界面
- [ ] 可以输入消息并发送给AI
- [ ] AI能够正常回复消息
- [ ] 对话历史在当前会话中正确保持
- [ ] 清空功能正常工作
- [ ] 错误处理机制有效（网络错误、API错误等）

### 界面验收
- [ ] 界面布局合理，用户体验良好
- [ ] 中文字体显示正常
- [ ] 窗口大小和位置合适
- [ ] 按钮和输入框响应正常

### 技术验收
- [ ] 代码结构清晰，模块分离良好
- [ ] 配置管理正常工作
- [ ] 日志记录功能正常
- [ ] 异常处理完善

---

**🎯 第一阶段目标**: 构建一个稳定、可用的基础AI聊天应用，为后续阶段的功能扩展打下坚实基础。
