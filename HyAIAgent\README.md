# HyAIAgent - 第一阶段：基础AI问答系统

## 🎯 项目简介

HyAIAgent是一个基于Python和PyQt6的智能AI助手应用。第一阶段实现了基础的AI问答功能，用户可以通过图形界面与OpenAI GPT模型进行对话。

## ✨ 功能特性

- 🤖 支持OpenAI GPT模型对话
- 🖥️ 友好的PyQt6图形界面
- ⚙️ 灵活的配置管理系统
- 💾 轻量级KV数据库存储
- 📝 对话历史管理
- 🔧 完善的错误处理和日志系统

## 🏗️ 项目结构

```
HyAIAgent/
├── main.py                    # 程序入口
├── config.json               # 配置文件
├── .env                       # 环境变量（API密钥）
├── requirements.txt           # 依赖包
├── README.md                  # 使用说明
├── core/                      # 核心模块
│   ├── __init__.py
│   ├── config_manager.py     # 配置管理
│   ├── ai_client.py          # AI客户端
│   ├── kv_store.py           # KV数据库
│   └── prompt_manager.py     # 提示词管理
├── prompts/                   # 提示词目录
│   ├── global/               # 全局提示词
│   │   └── basic_chat.md     # 基础聊天提示词
│   └── templates/            # 提示词模板
├── data/                      # 数据目录
│   └── kv_store.json         # KV数据库文件
└── ui/                        # 用户界面
    ├── __init__.py
    └── chat_window.py        # 聊天窗口
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

1. 复制 `.env` 文件并添加你的API密钥：
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

2. 根据需要修改 `config.json` 中的配置

### 3. 启动应用

```bash
python main.py
```

## ⚙️ 配置说明

### config.json 主要配置项

- `application`: 应用基本信息
- `ai_providers`: AI服务商配置
- `kv_database`: 数据库配置
- `ui`: 界面配置
- `logging`: 日志配置

### 环境变量

在 `.env` 文件中设置以下环境变量：

- `OPENAI_API_KEY`: OpenAI API密钥
- `DEEPSEEK_API_KEY`: DeepSeek API密钥（可选）
- `ZHIPU_API_KEY`: 智谱AI API密钥（可选）

## 📝 使用说明

1. 启动应用后，会显示聊天窗口
2. 在输入框中输入你的问题
3. 点击"发送"按钮或按回车键发送消息
4. AI会在聊天区域回复你的问题
5. 使用"清空"按钮可以清除对话历史

## 🔧 开发说明

本项目采用模块化设计，主要模块包括：

- **ConfigManager**: 配置管理器
- **SimpleAIClient**: AI客户端
- **KVStore**: 轻量级数据库
- **PromptManager**: 提示词管理
- **ChatWindow**: 聊天界面

## 📄 许可证

本项目采用MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**版本**: 1.0.0-stage1  
**更新时间**: 2025-01-27
